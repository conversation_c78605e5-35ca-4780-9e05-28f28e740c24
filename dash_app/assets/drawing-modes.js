// Drawing Modes Module
// Handles mode management (normal, line, trendline, fib, rect) and UI state

(function() {
  'use strict';

  let currentMode = 'normal';
  let setPanZoomEnabled = null;

  // Get UI elements
  function getUIElements() {
    return {
      btnNormal: document.getElementById('mode-normal'),
      btnLine: document.getElementById('mode-line'),
      btnTrendline: document.getElementById('mode-trendline'),
      btnFib: document.getElementById('mode-fib'),
      btnRect: document.getElementById('mode-rect'),
      btnAutoFib: document.getElementById('auto-fib'),
      btnClear: document.getElementById('clear'),
      btnDrawingsToggle: document.getElementById('drawings-toggle'),
      drawingsPanel: document.getElementById('drawings-panel'),
      hintEl: document.getElementById('hint'),
      tradeInfoEl: document.getElementById('trade-info')
    };
  }

  // Set the current drawing mode
  function setMode(newMode) {
    console.log('[drawing-modes] setMode called with:', newMode);

    const elements = getUIElements();

    currentMode = newMode;
    elements.btnNormal.classList.toggle('active', currentMode === 'normal');
    elements.btnLine.classList.toggle('active', currentMode === 'line');
    elements.btnTrendline.classList.toggle('active', currentMode === 'trendline');
    elements.btnFib.classList.toggle('active', currentMode === 'fib');
    elements.btnRect.classList.toggle('active', currentMode === 'rect');

    // Enable/disable pan and zoom based on mode
    console.log('[drawing-modes] setPanZoomEnabled function:', typeof setPanZoomEnabled);
    if (setPanZoomEnabled && typeof setPanZoomEnabled === 'function') {
      try {
        setPanZoomEnabled(currentMode === 'normal');
        console.log('[drawing-modes] setPanZoomEnabled called successfully');
      } catch (error) {
        console.error('[drawing-modes] Error calling setPanZoomEnabled:', error);
      }
    } else {
      console.warn('[drawing-modes] setPanZoomEnabled not available or not a function');
    }

    // Update hint text based on mode
    const hintTexts = {
      'normal': 'Mode: Normal — chart viewing mode with pan and zoom enabled.',
      'line': 'Mode: Line — click anywhere to drop a horizontal line at the exact price.',
      'trendline': 'Mode: Trendline — click and drag to draw a diagonal trendline. Useful for trend analysis and support/resistance levels.',
      'rect': 'Mode: Rectangle — click and drag to draw a rectangle. Useful for highlighting price ranges or time periods.',
      'fib': 'Mode: Fib — mouse down to set P1, drag, mouse up to set P2. Creates P1, P2, 50%, 61.8%, 78.6% retracements and -23.6%, -38.2% take profit levels. Shift+click trash on any fib level to delete the whole set.'
    };

    elements.hintEl.textContent = hintTexts[currentMode] || '';
  }

  // Initialize mode buttons and event handlers
  function initializeModeButtons() {
    const elements = getUIElements();
    
    elements.btnNormal.onclick = () => setMode('normal');
    elements.btnLine.onclick = () => setMode('line');
    elements.btnTrendline.onclick = () => setMode('trendline');
    elements.btnFib.onclick = () => setMode('fib');
    elements.btnRect.onclick = () => setMode('rect');
    
    // Set initial mode
    setMode('normal');
  }

  // Initialize drawings panel toggle
  function initializeDrawingsPanel() {
    const elements = getUIElements();
    
    // Toggle drawings panel
    elements.btnDrawingsToggle.onclick = () => {
      elements.drawingsPanel.classList.toggle('show');
    };

    // Close drawings panel when clicking outside
    document.addEventListener('click', (e) => {
      if (!elements.drawingsPanel.contains(e.target) && 
          !elements.btnDrawingsToggle.contains(e.target)) {
        elements.drawingsPanel.classList.remove('show');
      }
    });
  }

  // Get current mode
  function getCurrentMode() {
    return currentMode;
  }

  // Set pan/zoom function reference
  function setPanZoomFunction(fn) {
    console.log('[drawing-modes] setPanZoomFunction called with:', typeof fn);
    setPanZoomEnabled = fn;
  }

  // Initialize all mode-related functionality
  function initializeModes(panZoomFn) {
    setPanZoomFunction(panZoomFn);
    initializeModeButtons();
    initializeDrawingsPanel();
  }

  // Export to global scope
  window.DrawingModes = {
    initializeModes,
    setMode,
    getCurrentMode,
    getUIElements
  };

})();
