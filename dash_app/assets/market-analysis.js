// Market Analysis Module
// Handles swing point detection and market structure analysis

(function() {
  'use strict';

  // Find swing points in market data
  function findSwingPoints(data) {
    if (!data || data.length < 5) return { swingHighs: [], swingLows: [] };

    const swingHighs = [];
    const swingLows = [];
    const lookback = 2; // Look 2 candles back and forward for swing confirmation

    // Find local swing highs and lows
    for (let i = lookback; i < data.length - lookback; i++) {
      const candle = data[i];
      if (!candle.high || !candle.low || !candle.close) continue;

      // Check for swing high (higher than surrounding candles)
      let isSwingHigh = true;
      for (let j = i - lookback; j <= i + lookback; j++) {
        if (j !== i && data[j] && data[j].high >= candle.high) {
          isSwingHigh = false;
          break;
        }
      }

      if (isSwingHigh) {
        swingHighs.push({
          index: i,
          time: candle.time,
          price: candle.high,
          type: 'high'
        });
      }

      // Check for swing low (lower than surrounding candles)
      let isSwingLow = true;
      for (let j = i - lookback; j <= i + lookback; j++) {
        if (j !== i && data[j] && data[j].low <= candle.low) {
          isSwingLow = false;
          break;
        }
      }

      if (isSwingLow) {
        swingLows.push({
          index: i,
          time: candle.time,
          price: candle.low,
          type: 'low'
        });
      }
    }

    console.log('Found swing points:', {
      highs: swingHighs.length,
      lows: swingLows.length,
      swingHighs: swingHighs.map(h => h.price.toFixed(2)),
      swingLows: swingLows.map(l => l.price.toFixed(2))
    });

    return { swingHighs, swingLows };
  }

  // Find the most recent significant swing for Auto-Fib
  function findMostRecentSwing(data) {
    const { swingHighs, swingLows } = findSwingPoints(data);

    if (swingHighs.length === 0 || swingLows.length === 0) {
      console.log('Not enough swing points found');
      return null;
    }

    // Get the most recent swing high and low
    const lastHigh = swingHighs[swingHighs.length - 1];
    const lastLow = swingLows[swingLows.length - 1];

    console.log('Last swing high:', lastHigh.price.toFixed(2), 'at index', lastHigh.index);
    console.log('Last swing low:', lastLow.price.toFixed(2), 'at index', lastLow.index);

    // Find the most recent significant swing
    if (lastHigh.index > lastLow.index) {
      // Most recent swing is a high, find the previous low
      const previousLow = swingLows.reverse().find(low => low.index < lastHigh.index);
      swingLows.reverse(); // restore original order

      if (previousLow) {
        console.log('Bull swing detected:', previousLow.price.toFixed(2), 'to', lastHigh.price.toFixed(2));
        return {
          type: 'bull',
          from: previousLow,
          to: lastHigh
        };
      }
    } else {
      // Most recent swing is a low, find the previous high
      const previousHigh = swingHighs.reverse().find(high => high.index < lastLow.index);
      swingHighs.reverse(); // restore original order

      if (previousHigh) {
        console.log('Bear swing detected:', previousHigh.price.toFixed(2), 'to', lastLow.price.toFixed(2));
        return {
          type: 'bear',
          from: previousHigh,
          to: lastLow
        };
      }
    }

    console.log('No valid swing pattern found');
    return null;
  }

  // Analyze market structure for trend identification
  function analyzeMarketStructure(data) {
    const { swingHighs, swingLows } = findSwingPoints(data);
    
    if (swingHighs.length < 2 || swingLows.length < 2) {
      return { trend: 'neutral', confidence: 0 };
    }

    // Analyze recent swing highs for uptrend
    const recentHighs = swingHighs.slice(-3); // Last 3 swing highs
    const recentLows = swingLows.slice(-3); // Last 3 swing lows

    let higherHighs = 0;
    let higherLows = 0;
    let lowerHighs = 0;
    let lowerLows = 0;

    // Check for higher highs
    for (let i = 1; i < recentHighs.length; i++) {
      if (recentHighs[i].price > recentHighs[i-1].price) {
        higherHighs++;
      } else {
        lowerHighs++;
      }
    }

    // Check for higher lows
    for (let i = 1; i < recentLows.length; i++) {
      if (recentLows[i].price > recentLows[i-1].price) {
        higherLows++;
      } else {
        lowerLows++;
      }
    }

    // Determine trend
    let trend = 'neutral';
    let confidence = 0;

    if (higherHighs > lowerHighs && higherLows > lowerLows) {
      trend = 'uptrend';
      confidence = (higherHighs + higherLows) / (recentHighs.length + recentLows.length - 2);
    } else if (lowerHighs > higherHighs && lowerLows > higherLows) {
      trend = 'downtrend';
      confidence = (lowerHighs + lowerLows) / (recentHighs.length + recentLows.length - 2);
    }

    return {
      trend,
      confidence,
      swingHighs: recentHighs,
      swingLows: recentLows,
      analysis: {
        higherHighs,
        higherLows,
        lowerHighs,
        lowerLows
      }
    };
  }

  // Find support and resistance levels
  function findSupportResistance(data, tolerance = 0.01) {
    const { swingHighs, swingLows } = findSwingPoints(data);
    const allLevels = [...swingHighs, ...swingLows];
    
    if (allLevels.length < 3) return { support: [], resistance: [] };

    // Group similar price levels
    const levels = [];
    
    for (const level of allLevels) {
      const existingLevel = levels.find(l => 
        Math.abs(l.price - level.price) / level.price < tolerance
      );
      
      if (existingLevel) {
        existingLevel.touches++;
        existingLevel.prices.push(level.price);
        existingLevel.avgPrice = existingLevel.prices.reduce((a, b) => a + b) / existingLevel.prices.length;
      } else {
        levels.push({
          price: level.price,
          avgPrice: level.price,
          touches: 1,
          prices: [level.price],
          type: level.type,
          lastTouch: level.time
        });
      }
    }

    // Filter levels with multiple touches (more significant)
    const significantLevels = levels.filter(l => l.touches >= 2);
    
    // Separate into support and resistance
    const currentPrice = data[data.length - 1].close;
    const support = significantLevels
      .filter(l => l.avgPrice < currentPrice)
      .sort((a, b) => b.avgPrice - a.avgPrice); // Closest to current price first
      
    const resistance = significantLevels
      .filter(l => l.avgPrice > currentPrice)
      .sort((a, b) => a.avgPrice - b.avgPrice); // Closest to current price first

    return { support, resistance };
  }

  // Calculate volatility metrics
  function calculateVolatility(data, period = 20) {
    if (data.length < period) return null;

    const returns = [];
    for (let i = 1; i < data.length; i++) {
      const return_ = Math.log(data[i].close / data[i-1].close);
      returns.push(return_);
    }

    // Calculate standard deviation of returns
    const mean = returns.reduce((a, b) => a + b) / returns.length;
    const variance = returns.reduce((sum, return_) => sum + Math.pow(return_ - mean, 2), 0) / returns.length;
    const volatility = Math.sqrt(variance) * Math.sqrt(252); // Annualized volatility

    return {
      volatility,
      mean,
      variance,
      period: returns.length
    };
  }

  // Export to global scope
  window.MarketAnalysis = {
    findSwingPoints,
    findMostRecentSwing,
    analyzeMarketStructure,
    findSupportResistance,
    calculateVolatility
  };

})();
