// Chart Initialization Module
// Handles chart creation, configuration, and basic setup

(function() {
  'use strict';

  // Chart initialization and configuration
  function initialize<PERSON>hart() {
    // ----- <PERSON><PERSON> guard to avoid duplicates on reload -----
    if (window.__lw_chart__) {
      try { window.__lw_chart__.remove(); } catch(e){}
      const chartEl0 = document.getElementById('chart');
      while (chartEl0.firstChild) chartEl0.removeChild(chartEl0.firstChild);
    }

    // ----- Elements -----
    const chartEl = document.getElementById('chart');
    
    // ----- Create chart -----
    const chart = window.__lw_chart__ = LightweightCharts.createChart(chartEl, {
      layout: { background: { color: '#0e1116' }, textColor: '#c9d1d9' },
      rightPriceScale: {
        autoScale: false,  // Disable auto-scaling to allow manual panning
      },
      timeScale: { borderVisible: false, timeVisible: true, secondsVisible: false },
      grid: { horzLines: { color: '#1f2633' }, vertLines: { color: '#1f2633' } },
      crosshair: { mode: LightweightCharts.CrosshairMode.Normal },
    });

    const series = chart.addCandlestickSeries({
      upColor: '#26a69a', downColor: '#ef5350',
      wickUpColor: '#26a69a', wickDownColor: '#ef5350',
      borderVisible: false,
      priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
    });

    return { chart, series, chartEl };
  }

  // Generate demo data and setup initial chart data
  function setupDemoData(series) {
    // ----- Demo data -----
    const data = [];
    const t0 = Math.floor(Date.now()/1000) - 3600*24*30; // 30d hourly
    let p = 100;
    for (let i=0;i<500;i++){
      const t = t0 + i*3600;
      const o = p, h = o + Math.random()*3+1, l = o - (Math.random()*3+1), c = l + Math.random()*(h-l);
      p = c;
      data.push({ time: t, open:o, high:h, low:l, close:c });
    }
    
    // Add whitespace points to allow drawing beyond last candle
    const lastTs = data[data.length - 1].time;
    const candleInterval = data.length > 1 ? data[1].time - data[0].time : 3600;

    // Add several whitespace points to create drawing space
    let numWhiteSpaces = 200;
    const whitespacePoints = [];
    for (let i = 1; i <= numWhiteSpaces; i++) {
      whitespacePoints.push({ time: lastTs + (candleInterval * i) });
    }

    const extendedData = data.concat(whitespacePoints);
    series.setData(extendedData);

    return { data, lastTs, candleInterval, numWhiteSpaces };
  }

  // Setup initial visible range and baseline trendline
  function setupChartView(chart, series, data, lastTs, candleInterval, numWhiteSpaces) {
    // Set initial visible range to show some future space
    const futureTime = lastTs + (candleInterval * 7);
    chart.timeScale().setVisibleRange({
      from: data[Math.max(0, data.length - numWhiteSpaces)].time,
      to: futureTime
    });

    // Create baseline trendline to establish drawable area in future space
    // This prevents pan gesture from taking over when drawing beyond last candle
    const baselineTrendlineSeries = chart.addLineSeries({
      color: 'transparent', // Blue color so we can see it working
      lineWidth: 1,
      priceLineVisible: false,
      lastValueVisible: false,
      title: ''
    });

    // Create a horizontal baseline trendline at current price level
    const lastPrice = data[data.length - 1].close;
    const baselinePrice = lastPrice; // Horizontal line at current price

    // Extend baseline to the end of whitespace area to ensure full coverage
    const maxWhitespaceTime = lastTs + (candleInterval * numWhiteSpaces); // End of our whitespace points

    baselineTrendlineSeries.setData([
      { time: lastTs, value: baselinePrice },
      { time: maxWhitespaceTime, value: baselinePrice } // Extend to end of whitespace
    ]);

    console.log('Created horizontal baseline trendline for future drawing area:', {
      from: lastTs,
      to: maxWhitespaceTime,
      price: baselinePrice,
      lastPrice: lastPrice,
      whitespaceEnd: new Date(maxWhitespaceTime * 1000).toISOString()
    });
  }

  // Toggle chart interactions (pan/zoom) while drawing
  function setPanZoomEnabled(chart, chartEl, enabled) {
    chart.applyOptions({
      handleScroll: enabled,  // disable pressed-mouse pan
      handleScale: enabled,   // disable wheel/pinch zoom
    });

    // Optional: change cursor to signal drawing mode
    chartEl.style.cursor = enabled ? "default" : "crosshair";
  }

  // Setup responsive behavior
  function setupResponsive(chart, chartEl) {
    const ro = new ResizeObserver(entries=>{
      const { width, height } = entries[0].contentRect;
      chart.applyOptions({ width, height });
    });
    ro.observe(chartEl);
  }

  // Expose external update function
  function setupExternalAPI(series) {
    window.updateChartData = function(newData) {
      if (newData && Array.isArray(newData)) {
        series.setData(newData);
      }
    };
  }

  // Main initialization function
  function initChart() {
    const { chart, series, chartEl } = initializeChart();
    const { data, lastTs, candleInterval, numWhiteSpaces } = setupDemoData(series);
    
    setupChartView(chart, series, data, lastTs, candleInterval, numWhiteSpaces);
    setupResponsive(chart, chartEl);
    setupExternalAPI(series);

    return {
      chart,
      series,
      chartEl,
      data,
      lastTs,
      candleInterval,
      numWhiteSpaces,
      setPanZoomEnabled: (enabled) => setPanZoomEnabled(chart, chartEl, enabled)
    };
  }

  // Export to global scope
  window.ChartInit = {
    initChart,
    setPanZoomEnabled
  };

})();
