:root { --bg:#0e1116; --panel:#161b22; --text:#c9d1d9; --muted:#8b949e; --accent:#58a6ff; }
html, body { height:100%; }
body { margin:0; display:flex; flex-direction:column; height:100vh;
       background:var(--bg); color:var(--text); font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; }
#chart { width:100%; flex: 1 1 auto; min-height:300px; }
.top-controls { background:var(--panel); border-bottom:1px solid #222; padding:10px 12px; }
.controls-row { display:flex; gap:8px; flex-wrap:wrap; align-items:center; justify-content:flex-start; }
.controls-row .btn { background:#222833; color:var(--text); border:1px solid #2b3340; padding:6px 10px; border-radius:8px; cursor:pointer; }
.controls-row .btn:hover { border-color:#3a4556; }
.controls-row .btn.active { outline:2px solid #58a6ff60; }
.hint-text { color:var(--muted); font-size:12px; margin-left:auto; }
.drawings-panel { position:absolute; top:100px; right:10px; background:var(--panel); border:1px solid #222; border-radius:8px; padding:10px; min-width:280px; max-height:400px; overflow-y:auto; z-index:1000; display:none; }
.drawings-panel.show { display:block; }
.info-section { background:var(--panel); border-bottom:1px solid #222; padding:10px 12px; display:grid; grid-template-columns:1fr 1fr; gap:20px; }
.mode-info, .trade-details { }
.mode-info h4, .trade-details h4 { margin:0 0 8px 0; font-size:14px; color:var(--text); }
.trade-entry { background:#0b1d33; border:1px solid #14304f; border-radius:6px; padding:8px; margin:4px 0; font-size:12px; }
.trade-entry.stop-loss { border-color:#dc3545; background:#2d0a0f; }
.trade-entry.entry { border-color:#28a745; background:#0a2d0f; }
.trade-entry.take-profit { border-color:#ffc107; background:#2d2a0a; }
.btn { background:#222833; color:var(--text); border:1px solid #2b3340; padding:6px 10px; border-radius:8px; cursor:pointer; }
.btn:hover { border-color:#3a4556; }
.btn.active { outline:2px solid #58a6ff60; }
.lines-toggle { background:#222833; color:var(--text); border:1px solid #2b3340; padding:6px 10px; border-radius:8px; cursor:pointer; margin-left:10px; }
.lines-toggle:hover { border-color:#3a4556; }
.muted { color:var(--muted); font-size:12px; }
ul { list-style:none; padding:0; margin:8px 12px 12px; }
li { display:flex; justify-content:space-between; align-items:center; gap:8px; padding:6px 8px; border:1px solid #2b3340; border-radius:8px; margin-top:8px; }
.pill { background:#0b1d33; border:1px solid #14304f; border-radius:999px; padding:2px 8px; font-variant-numeric: tabular-nums; }
.groupTag { font-size:11px; opacity:0.7; border:1px solid #2b3340; border-radius:6px; padding:0 6px; margin-left:6px; }
.stack { display:flex; gap:8px; flex-wrap:wrap; }
