function init() {
  const el = document.getElementById('chart');
  if (!el) { return; } // no-op in parent context

  const needed = ['ChartInit','LineManagement','DrawingModes','Fibonacci','Trendline','Rectangle','TradeDetails'];
  const missing = needed.filter(n => typeof window[n] === 'undefined');
  if (missing.length > 0) {
    if (!window.__charting_init_retries) window.__charting_init_retries = 0;
    if (window.__charting_init_retries < 50) { window.__charting_init_retries += 1; setTimeout(init, 100); }
    else { console.error('Chart modules failed to load:', missing); }
    return;
  }
  initializeChart();
}