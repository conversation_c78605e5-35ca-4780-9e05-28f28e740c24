// Main Charting Module
// Coordinates all charting functionality and modules

(function(){
  'use strict';

  // Main chart variables
  let chart = null;
  let series = null;
  let chartEl = null;
  let lastPoint = null; // last crosshair point {x,y}
  let setPanZoomEnabledFn = null; // store the closure function

  // Initialize all modules and set up the chart
  function initializeChart() {
    console.log('[charting] initializeChart() called');

    // Initialize chart and get references
    const chartInit = window.ChartInit.initChart();
    chart = chartInit.chart;
    series = chartInit.series;
    chartEl = chartInit.chartEl;
    setPanZoomEnabledFn = chartInit.setPanZoomEnabled; // store the closure

    // Initialize all modules with chart references
    LineManagement.initialize(chart, series);
    DrawingModes.initializeModes(setPanZoomEnabledFn);
    Fibonacci.initialize(chart, series, chartEl);
    Trendline.initialize(chart, series, chartEl);
    Rectangle.initialize(chart, series, chartEl);
    TradeDetails.initialize();

    // Set up event handlers
    setupEventHandlers();
    setupUIHandlers();

    console.log('Chart and all modules initialized successfully');
  }

  // Set up chart event handlers
  function setupEventHandlers() {
    // Track last crosshair point and update drawing modules
    chart.subscribeCrosshairMove((param) => {
      if (param?.point && typeof param.point.y === 'number') {
        lastPoint = param.point;

        // Update all drawing modules with current point
        Fibonacci.setLastPoint(lastPoint);
        Trendline.setLastPoint(lastPoint);
        Rectangle.setLastPoint(lastPoint);

        // Update active drawings
        Fibonacci.updateFibDrawing();
        Trendline.updateTrendlineDrawing();
        Rectangle.updateRectangleDrawing();
      }
    });

    // Handle single line clicks (Line mode only)
    chart.subscribeClick((param) => {
      const mode = DrawingModes.getCurrentMode();
      if (mode !== 'line') return;
      if (!param?.point || typeof param.point.y !== 'number') return;

      const price = LineManagement.snap(series.coordinateToPrice(param.point.y));
      const clickTime = chart.timeScale().coordinateToTime(param.point.x);

      if (clickTime == null) {
        console.error('Invalid time coordinate for line placement');
        return;
      }

      // Create time-bounded line starting from click point
      const currentData = series.data();
      const lastTime = currentData && currentData.length > 0 ?
        currentData[currentData.length - 1].time : clickTime;
      const lineEndTime = Math.max(clickTime, lastTime + (3600 * 24 * 7)); // Extend 7 days into future

      LineManagement.createFibLevel(price, clickTime, lineEndTime, {
        color:'#58a6ff',
        kind:'single',
        title:`Line ${price.toFixed(2)}`
      });

      console.log('Time-bounded line placed at', price, 'from time', clickTime);

      // Automatically switch back to normal mode after placing a line
      DrawingModes.setMode('normal');
    });

    // Handle drawing start (mousedown)
    chartEl.addEventListener('mousedown', (e) => {
      const mode = DrawingModes.getCurrentMode();

      if (mode === 'trendline') {
        if (Trendline.startTrendlineDrawing(lastPoint, setPanZoomEnabledFn)) {
          e.preventDefault();
          e.stopPropagation();
        }
      } else if (mode === 'fib') {
        if (Fibonacci.startFibDrawing(lastPoint, setPanZoomEnabledFn)) {
          e.preventDefault();
          e.stopPropagation();
        }
      } else if (mode === 'rect') {
        if (Rectangle.startRectangleDrawing(lastPoint, setPanZoomEnabledFn)) {
          e.preventDefault();
          e.stopPropagation();
        }
      }
    });

    // Handle drawing completion (mouseup)
    document.addEventListener('mouseup', () => {
      if (Trendline.isTrendlineActive()) {
        if (Trendline.completeTrendlineDrawing(setPanZoomEnabledFn)) {
          DrawingModes.setMode('normal');
        }
      } else if (Fibonacci.isFibActive()) {
        if (Fibonacci.completeFibDrawing(setPanZoomEnabledFn)) {
          DrawingModes.setMode('normal');
        }
      } else if (Rectangle.isRectangleActive()) {
        if (Rectangle.completeRectangleDrawing(setPanZoomEnabledFn)) {
          DrawingModes.setMode('normal');
        }
      }
    });
  }

  // Set up UI button handlers
  function setupUIHandlers() {
    const elements = DrawingModes.getUIElements();

    // Auto-Fib button handler
    if (elements.btnAutoFib) {
      elements.btnAutoFib.onclick = () => {
        Fibonacci.createAutoFib();
      };
    }

    // Clear all button handler
    if (elements.btnClear) {
      elements.btnClear.onclick = () => {
        LineManagement.clearAllLines();
      };
    }
  }

  // Initialize everything when DOM is ready
  function init() {
    console.log('[charting] init() called');
    
    // Check if DOM is ready and chart element exists
    const chartEl = document.getElementById('chart');
    if (!chartEl) {
      console.log('[charting] Chart element not found, waiting for DOM...');
      setTimeout(init, 100);
      return;
    }

    console.log('[charting] Chart element found, checking modules...');

    // Wait for all modules to be loaded
    if (typeof window.ChartInit === 'undefined' ||
        typeof window.LineManagement === 'undefined' ||
        typeof window.DrawingModes === 'undefined' ||
        typeof window.Fibonacci === 'undefined' ||
        typeof window.Trendline === 'undefined' ||
        typeof window.Rectangle === 'undefined' ||
        typeof window.TradeDetails === 'undefined') {
      
      console.log('[charting] Waiting for modules to load...');
      setTimeout(init, 100);
      return;
    }

    console.log('[charting] All modules loaded, calling initializeChart...');
    initializeChart();
  }

  // Start initialization - wait for window load to ensure iframe is ready
  if (document.readyState === 'complete') {
    console.log('[charting] Document already complete, calling init...');
    init();
  } else {
    console.log('[charting] Waiting for window load...');
    window.addEventListener('load', init);
  }

})();
