// Line Management Module
// Handles line creation, removal, and list management

(function() {
  'use strict';

  let chart = null;
  let series = null;
  let listEl = null;
  const lines = []; // { id, price, handle, kind: 'single'|'fib1'|'fib2'|'fib50'|'fib618'|'fib786'|'fibtp236'|'fibtp618'|'rectangle'|'trendline', group?:string }

  // Initialize line management with chart references
  function initialize(chartInstance, seriesInstance) {
    chart = chartInstance;
    series = seriesInstance;
    listEl = document.getElementById('list');
  }

  // Helper functions
  const pf = () => series ? series.options().priceFormat || {} : {};
  const minMove = () => pf().minMove ?? 0.01;
  const fmt = (x) => Number(x).toLocaleString(undefined, { maximumFractionDigits: 8 });
  const snap = (price) => Math.round(price / minMove()) * minMove();

  // Post message to parent
  function post(type, payload) {
    try { window.parent?.postMessage({ type, ...payload }, '*'); } catch(e){}
  }

  // Create a standard price line
  function createLine(price, opts = {}) {
    if (!series) return null;

    const handle = series.createPriceLine({
      price,
      color: opts.color || '#58a6ff',
      lineWidth: opts.lineWidth ?? 2,
      lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
      axisLabelVisible: true,
      title: opts.title ?? String(price),
    });
    
    const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
    const rec = { id, price, handle, kind: opts.kind || 'single', group: opts.group };
    lines.push(rec);

    // Only add list item for single lines, fib sets, rectangles, and trendlines (not individual fib/rect levels)
    if (opts.kind === 'single' || opts.kind === 'fibset' || opts.kind === 'rectangle' || opts.kind === 'trendline') {
      addListItem(rec);
    }

    post('lw_line_added', { price, kind: rec.kind, group: rec.group });
    return rec;
  }

  // Create time-bounded Fibonacci level (horizontal line between two time points)
  function createFibLevel(price, startTime, endTime, opts = {}) {
    if (!chart) return null;

    const fibSeries = chart.addLineSeries({
      color: opts.color || '#58a6ff',
      lineWidth: opts.lineWidth ?? 2,
      lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
      priceLineVisible: false,
      lastValueVisible: false,
      title: opts.title || `Fib ${price.toFixed(2)}`
    });

    // Create horizontal line data from start time to end time (or future)
    const currentData = series.data();
    const lastTime = currentData && currentData.length > 0 ?
      currentData[currentData.length - 1].time : endTime;
    const fibEndTime = Math.max(endTime, lastTime + (3600 * 24 * 7)); // Extend 7 days into future

    fibSeries.setData([
      { time: startTime, value: price },
      { time: fibEndTime, value: price }
    ]);

    const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
    const rec = {
      id,
      price,
      handle: fibSeries,
      kind: opts.kind || 'fibLevel',
      group: opts.group,
      startTime,
      endTime: fibEndTime
    };
    lines.push(rec);

    // Add list item for single lines, but not for individual fib levels
    if (opts.kind === 'single') {
      addListItem(rec);
    }

    post('lw_line_added', { price, kind: rec.kind, group: rec.group });
    return rec;
  }

  // Create a proper rectangle using LineSeries
  function createRectangle(startTime, endTime, topPrice, bottomPrice, opts = {}) {
    if (!chart) return null;

    const rectSeries = chart.addLineSeries({
      color: opts.color || '#58a6ff',
      lineWidth: opts.lineWidth ?? 2,
      lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
      priceLineVisible: false,
      lastValueVisible: false,
      title: opts.title || 'Rectangle'
    });

    // Create rectangle outline data points
    const rectangleData = [
      { time: startTime, value: topPrice },    // Top-left
      { time: endTime, value: topPrice },      // Top-right
      { time: endTime, value: bottomPrice },   // Bottom-right
      { time: startTime, value: bottomPrice }, // Bottom-left
      { time: startTime, value: topPrice }     // Close the rectangle
    ];

    rectSeries.setData(rectangleData);

    const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
    const rec = {
      id,
      handle: rectSeries,
      kind: 'rectangle',
      group: opts.group,
      startTime,
      endTime,
      topPrice,
      bottomPrice
    };
    lines.push(rec);
    addListItem(rec);

    post('lw_line_added', { kind: rec.kind, group: rec.group });
    return rec;
  }

  // Remove line by ID
  function removeLineById(id) {
    const idx = lines.findIndex(l => l.id === id);
    if (idx === -1) return;
    const rec = lines[idx];

    // Handle different types of chart elements
    if (rec.handle) {
      // Check if it's a LineSeries-based element (all Fibonacci levels, rectangles, and single lines)
      const isLineSeries = rec.kind === 'fibLevel' || rec.kind === 'fib1' || rec.kind === 'fib2' ||
                          rec.kind === 'fib50' || rec.kind === 'fib618' || rec.kind === 'fib786' ||
                          rec.kind === 'fibtp236' || rec.kind === 'fibtp382' || rec.kind === 'single' ||
                          rec.kind === 'rectangle';

      if (isLineSeries) {
        // Remove LineSeries-based elements
        try { chart.removeSeries(rec.handle); } catch(e){}
      } else {
        // Remove price line (trendlines don't have handles, they're managed by group removal)
        try { series.removePriceLine(rec.handle); } catch(e){}
      }
    }

    // Handle trendline series removal
    if (rec.kind === 'trendline' && rec.trendlineSeries) {
      try { chart.removeSeries(rec.trendlineSeries); } catch(e){}
    }

    lines.splice(idx, 1);
    const el = document.getElementById('li-' + id);
    if (el) el.remove();
    post('lw_line_removed', { id, kind: rec.kind, group: rec.group });
  }

  // Remove group of lines
  function removeGroup(groupId) {
    const groupLines = lines.filter(l => l.group === groupId);

    // Check if this group contains a Fibonacci set
    const fibInGroup = groupLines.find(l => l.kind === 'fibset' ||
      l.kind === 'fib1' || l.kind === 'fib2' || l.kind === 'fib50' ||
      l.kind === 'fib618' || l.kind === 'fib786' || l.kind === 'fibtp236' || l.kind === 'fibtp382');

    if (fibInGroup) {
      // Clear trade details when removing a Fibonacci set
      const tradeInfoEl = document.getElementById('trade-info');
      if (tradeInfoEl) {
        tradeInfoEl.innerHTML = '<div class="muted">Draw a Fibonacci retracement to generate trade plan</div>';
      }
    }

    // Handle rectangle markers removal
    const rectangleInGroup = groupLines.find(l => l.kind === 'rectangle');
    if (rectangleInGroup && rectangleInGroup.markers) {
      try {
        // Get existing markers and filter out the rectangle's markers
        const existingMarkers = series.markers?.() || [];
        const filteredMarkers = existingMarkers.filter(marker =>
          !rectangleInGroup.markers.some(rectMarker =>
            rectMarker.time === marker.time && rectMarker.text === marker.text
          )
        );
        series.setMarkers(filteredMarkers);
      } catch(e) {
        console.log('Error removing rectangle markers:', e);
      }
    }

    // Handle trendline series removal
    const trendlineInGroup = groupLines.find(l => l.kind === 'trendline');
    if (trendlineInGroup && trendlineInGroup.trendlineSeries) {
      try {
        chart.removeSeries(trendlineInGroup.trendlineSeries);
      } catch(e) {
        console.log('Error removing trendline series:', e);
      }
    }

    const toRemove = groupLines.map(l => l.id);
    toRemove.forEach(removeLineById);
  }

  // Add item to the list UI
  function addListItem(rec) {
    if (!listEl) return;

    const li = document.createElement('li');
    li.id = 'li-' + rec.id;

    const left = document.createElement('div');
    if (rec.kind === 'fibset') {
      left.textContent = 'Fib Set ';
      const pill = document.createElement('span');
      pill.className='pill';
      pill.textContent = `P1: ${fmt(rec.p1)} → P2: ${fmt(rec.p2)}`;
      left.appendChild(pill);
    } else if (rec.kind === 'rectangle') {
      left.textContent = 'Rectangle ';
      const pill = document.createElement('span');
      pill.className='pill';
      pill.textContent = `${fmt(rec.p1)} → ${fmt(rec.p2)}`;
      left.appendChild(pill);
    } else if (rec.kind === 'trendline') {
      left.textContent = 'Trendline ';
      const pill = document.createElement('span');
      pill.className='pill';
      pill.textContent = `${fmt(rec.p1)} → ${fmt(rec.p2)}`;
      left.appendChild(pill);
    } else {
      left.textContent = (rec.kind === 'single') ? 'Line @ ' : 'Line @ ';
      const pill = document.createElement('span'); pill.className='pill'; pill.textContent = fmt(rec.price);
      left.appendChild(pill);
    }

    if (rec.group && (rec.kind === 'fibset' || rec.kind === 'rectangle' || rec.kind === 'trendline')) {
      const tag = document.createElement('span');
      tag.className = 'groupTag';
      tag.textContent = rec.group.slice(0,6);
      left.appendChild(tag);
    }

    const right = document.createElement('div');

    if (rec.kind === 'fibset' || rec.kind === 'rectangle' || rec.kind === 'trendline') {
      // For fib sets, rectangles, and trendlines, only show delete button (no copy since it's multiple values)
      const del = document.createElement('button'); del.className='btn'; del.textContent='🗑️';
      del.onclick = () => removeGroup(rec.group);
      right.appendChild(del);
    } else {
      // For single lines, show both copy and delete
      const copy = document.createElement('button'); copy.className='btn'; copy.textContent='Copy';
      copy.onclick = () => { navigator.clipboard.writeText(String(rec.price)); copy.textContent='✔'; setTimeout(()=>copy.textContent='Copy',800); };

      const del = document.createElement('button'); del.className='btn'; del.textContent='🗑️';
      del.onclick = () => removeLineById(rec.id);

      right.append(copy, del);
    }

    li.append(left, right);
    listEl.appendChild(li);
  }

  // Clear all lines
  function clearAllLines() {
    // remove all lines & list
    for (const l of [...lines]) {
      // Check if it's a LineSeries-based element (all Fibonacci levels, rectangles, and single lines)
      const isLineSeries = l.kind === 'fibLevel' || l.kind === 'fib1' || l.kind === 'fib2' ||
                          l.kind === 'fib50' || l.kind === 'fib618' || l.kind === 'fib786' ||
                          l.kind === 'fibtp236' || l.kind === 'fibtp382' || l.kind === 'single' ||
                          l.kind === 'rectangle';

      if (isLineSeries) {
        try { chart.removeSeries(l.handle); } catch(e){}
      } else {
        try { series.removePriceLine(l.handle); } catch(e){}
      }
    }
    lines.length = 0;
    if (listEl) listEl.innerHTML = '';

    // Clear trade details
    const tradeInfoEl = document.getElementById('trade-info');
    if (tradeInfoEl) {
      tradeInfoEl.innerHTML = '<div class="muted">Draw a Fibonacci retracement to generate trade plan</div>';
    }

    post('lw_lines_cleared', {});
  }

  // Get all lines
  function getAllLines() {
    return lines;
  }

  // Export to global scope
  window.LineManagement = {
    initialize,
    createLine,
    createFibLevel,
    createRectangle,
    removeLineById,
    removeGroup,
    clearAllLines,
    getAllLines,
    snap,
    fmt
  };

})();
