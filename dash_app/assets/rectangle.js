// Rectangle Module
// Handles rectangle drawing and management functionality

(function() {
  'use strict';

  let chart = null;
  let series = null;
  let chartEl = null;
  let lastPoint = null;

  // Rectangle drawing state
  let rectActive = false;
  let rectStartPoint = null;
  let tempRectTop = null;
  let tempRectBottom = null;
  let tempRectLeft = null;
  let lastRectUpdate = 0;
  let rectUpdateTimeout = null;

  // Initialize rectangle functionality
  function initialize(chartInstance, seriesInstance, chartElement) {
    chart = chartInstance;
    series = seriesInstance;
    chartEl = chartElement;
  }

  // Set last crosshair point
  function setLastPoint(point) {
    lastPoint = point;
  }

  // Start rectangle drawing
  function startRectangleDrawing(point, setPanZoomEnabled) {
    if (!point || typeof point.y !== 'number') return false;

    // Disable pan/zoom while dragging Rectangle
    setPanZoomEnabled(false);

    rectActive = true;
    const time = chart.timeScale().coordinateToTime(point.x);
    const price = LineManagement.snap(series.coordinateToPrice(point.y));
    rectStartPoint = { x: point.x, y: point.y, time, price };

    // Create initial temp lines + left vertical line
    tempRectTop = series.createPriceLine({
      price, 
      color:'#cccccc', 
      lineWidth:1, 
      lineStyle:2, 
      axisLabelVisible:true, 
      title:'Rect Start'
    });
    tempRectBottom = tempRectTop; // Start with same line, will split on mousemove

    // Create left vertical line (shows rectangle start)
    tempRectLeft = chart.addLineSeries({
      color: '#cccccc',
      lineWidth: 1,
      lineStyle: 2, // dashed
      priceLineVisible: false,
      lastValueVisible: false
    });

    // Set initial left line (just at start point)
    const currentData = series.data();
    if (currentData && currentData.length > 0) {
      const minPrice = Math.min(...currentData.map(d => d.low));
      const maxPrice = Math.max(...currentData.map(d => d.high));
      tempRectLeft.setData([
        { time, value: minPrice },
        { time, value: maxPrice }
      ]);
    }

    return true;
  }

  // Update rectangle drawing during mouse move
  function updateRectangleDrawing() {
    if (!rectActive || !rectStartPoint || !lastPoint || typeof lastPoint.y !== 'number') return;

    const now = Date.now();
    const currentPrice = LineManagement.snap(series.coordinateToPrice(lastPoint.y));
    const topPrice = Math.max(rectStartPoint.price, currentPrice);
    const bottomPrice = Math.min(rectStartPoint.price, currentPrice);

    // Always update horizontal lines (lightweight like Fib)
    if (tempRectTop) { 
      try { series.removePriceLine(tempRectTop); } catch(e){} 
      tempRectTop = null; 
    }
    if (tempRectBottom) { 
      try { series.removePriceLine(tempRectBottom); } catch(e){} 
      tempRectBottom = null; 
    }

    tempRectTop = series.createPriceLine({
      price: topPrice, 
      color:'#cccccc', 
      lineWidth:1, 
      lineStyle:2, 
      axisLabelVisible:true, 
      title:'Rect Top'
    });
    tempRectBottom = series.createPriceLine({
      price: bottomPrice, 
      color:'#cccccc', 
      lineWidth:1, 
      lineStyle:2, 
      axisLabelVisible:true, 
      title:'Rect Bottom'
    });

    // Throttle left vertical line updates (like trendline throttling)
    if (tempRectLeft && now - lastRectUpdate > 50) { // Update every 50ms max
      lastRectUpdate = now;
      try {
        tempRectLeft.setData([
          { time: rectStartPoint.time, value: topPrice },
          { time: rectStartPoint.time, value: bottomPrice }
        ]);
      } catch(e) {
        console.log('Throttled left line update error:', e);
      }
    }

    // Schedule a final update for when mouse stops moving (debounced)
    if (rectUpdateTimeout) clearTimeout(rectUpdateTimeout);
    rectUpdateTimeout = setTimeout(() => {
      if (tempRectLeft && rectActive) {
        try {
          tempRectLeft.setData([
            { time: rectStartPoint.time, value: topPrice },
            { time: rectStartPoint.time, value: bottomPrice }
          ]);
        } catch(e) {
          console.log('Final rect update error:', e);
        }
      }
    }, 100); // Final update 100ms after mouse stops
  }

  // Complete rectangle drawing
  function completeRectangleDrawing(setPanZoomEnabled) {
    if (!rectActive) return false;

    rectActive = false;

    // Re-enable pan/zoom
    setPanZoomEnabled(true);

    if (!lastPoint || typeof lastPoint.y !== 'number' || !rectStartPoint) {
      // cleanup temp rectangle lines (like Fib cleanup + left line)
      if (tempRectTop) { 
        try { series.removePriceLine(tempRectTop); } catch(e){} 
        tempRectTop = null; 
      }
      if (tempRectBottom && tempRectBottom !== tempRectTop) { 
        try { series.removePriceLine(tempRectBottom); } catch(e){} 
        tempRectBottom = null; 
      }
      if (tempRectLeft) { 
        try { chart.removeSeries(tempRectLeft); } catch(e){} 
        tempRectLeft = null; 
      }
      if (rectUpdateTimeout) { 
        clearTimeout(rectUpdateTimeout); 
        rectUpdateTimeout = null; 
      }
      rectStartPoint = null;
      return false;
    }

    const currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
    const currentPrice = LineManagement.snap(series.coordinateToPrice(lastPoint.y));
    const topPrice = Math.max(rectStartPoint.price, currentPrice);
    const bottomPrice = Math.min(rectStartPoint.price, currentPrice);
    const leftTime = Math.min(rectStartPoint.time, currentTime);
    const rightTime = Math.max(rectStartPoint.time, currentTime);
    const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

    // Remove temp rectangle lines (like Fib cleanup + left line)
    if (tempRectTop) { 
      try { series.removePriceLine(tempRectTop); } catch(e){} 
      tempRectTop = null; 
    }
    if (tempRectBottom && tempRectBottom !== tempRectTop) { 
      try { series.removePriceLine(tempRectBottom); } catch(e){} 
      tempRectBottom = null; 
    }
    if (tempRectLeft) { 
      try { chart.removeSeries(tempRectLeft); } catch(e){} 
      tempRectLeft = null; 
    }
    if (rectUpdateTimeout) { 
      clearTimeout(rectUpdateTimeout); 
      rectUpdateTimeout = null; 
    }

    // Create proper rectangle using LineSeries
    const rectangle = LineManagement.createRectangle(leftTime, rightTime, topPrice, bottomPrice, {
      color: '#58a6ff',
      lineWidth: 2,
      group,
      title: `Rectangle ${topPrice.toFixed(2)}-${bottomPrice.toFixed(2)}`
    });

    console.log('Rectangle created:', {
      startTime: leftTime,
      endTime: rightTime,
      topPrice,
      bottomPrice
    });

    rectStartPoint = null;
    return true;
  }

  // Check if rectangle is active
  function isRectangleActive() {
    return rectActive;
  }

  // Export to global scope
  window.Rectangle = {
    initialize,
    setLastPoint,
    startRectangleDrawing,
    updateRectangleDrawing,
    completeRectangleDrawing,
    isRectangleActive
  };

})();
