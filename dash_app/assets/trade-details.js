// Trade Details Module
// Handles trade plan generation and display functionality

(function() {
  'use strict';

  let tradeInfoEl = null;

  // Initialize trade details functionality
  function initialize() {
    tradeInfoEl = document.getElementById('trade-info');
  }

  // Generate trade details from Fibonacci levels
  function generateTradeDetails(p1, p2, fib50, fib618, fib786, fibtp236, fibtp382) {
    console.log('generateTradeDetails called with:', { p1, p2, fib50, fib618, fib786, fibtp236, fibtp382 });
    console.log('tradeInfoEl:', tradeInfoEl);

    if (!tradeInfoEl) {
      console.error('tradeInfoEl not found!');
      return;
    }

    // Clear any existing trade details first
    tradeInfoEl.innerHTML = '<div class="muted">Generating trade plan...</div>';

    // Determine if P1 is low and P2 is high (bullish setup)
    const isLowToHigh = p1 < p2;
    const accountSize = 10000;

    if (isLowToHigh) {
      // LONG: P1 (low) = Stop Loss, retracements = entries, extensions = take profits
      const stopLoss = p1;
      const entries = [
        { level: '50%', price: fib50, qty: 2 },
        { level: '61.8%', price: fib618, qty: 2 },
        { level: '78.6%', price: fib786, qty: 2 }
      ];
      const takeProfits = [
        { level: '23.6%', price: fibtp236, qty: 3, note: 'Half position' },
        { level: '38.2%', price: fibtp382, qty: 3, note: 'Remaining position' }
      ];

      displayTradeDetails('LONG', stopLoss, entries, takeProfits, accountSize);
    } else {
      // SHORT: P1 (high) = Stop Loss, retracements = entries, extensions = take profits
      const stopLoss = p1;
      const entries = [
        { level: '50%', price: fib50, qty: 2 },
        { level: '61.8%', price: fib618, qty: 2 },
        { level: '78.6%', price: fib786, qty: 2 }
      ];
      const takeProfits = [
        { level: '23.6%', price: fibtp236, qty: 3, note: 'Half position' },
        { level: '38.2%', price: fibtp382, qty: 3, note: 'Remaining position' }
      ];

      displayTradeDetails('SHORT', stopLoss, entries, takeProfits, accountSize);
    }
  }

  // Display formatted trade details
  function displayTradeDetails(direction, stopLoss, entries, takeProfits, accountSize) {
    // Calculate total position size and risk
    const totalQty = entries.reduce((sum, entry) => sum + entry.qty, 0);
    const avgEntryPrice = entries.reduce((sum, entry) => sum + (entry.price * entry.qty), 0) / totalQty;

    // Calculate risk per share and total risk
    const riskPerShare = Math.abs(avgEntryPrice - stopLoss);
    const totalRisk = riskPerShare * totalQty;
    const accountRisk = (totalRisk / accountSize) * 100;

    // Calculate potential profits
    const tp1Profit = Math.abs(takeProfits[0].price - avgEntryPrice) * takeProfits[0].qty;
    const tp2Profit = Math.abs(takeProfits[1].price - avgEntryPrice) * takeProfits[1].qty;
    const totalPotentialProfit = tp1Profit + tp2Profit;
    const riskRewardRatio = totalPotentialProfit / totalRisk;

    let html = `<div style="font-weight: bold; margin-bottom: 8px; color: ${direction === 'LONG' ? '#28a745' : '#dc3545'};">${direction} Trade Plan</div>`;

    // Risk Summary
    html += `<div style="background: #1a1a1a; border: 1px solid #333; border-radius: 4px; padding: 6px; margin-bottom: 8px; font-size: 11px;">
      <div><strong>Account Risk:</strong> ${accountRisk.toFixed(2)}% ($${totalRisk.toFixed(2)})</div>
      <div><strong>Risk/Reward:</strong> 1:${riskRewardRatio.toFixed(2)}</div>
      <div><strong>Potential Profit:</strong> $${totalPotentialProfit.toFixed(2)}</div>
    </div>`;

    // Stop Loss
    html += `<div class="trade-entry stop-loss">
      <strong>Stop Loss:</strong> ${stopLoss.toFixed(2)} <span style="font-size: 10px;">(Risk: $${totalRisk.toFixed(2)})</span>
    </div>`;

    // Entries
    html += `<div style="margin: 8px 0 4px 0; font-weight: bold; font-size: 12px;">Entries (Total Qty: ${totalQty}):</div>`;
    entries.forEach(entry => {
      const entryValue = entry.price * entry.qty;
      html += `<div class="trade-entry entry">
        ${entry.level}: ${entry.price.toFixed(2)} (Qty: ${entry.qty}) <span style="font-size: 10px;">$${entryValue.toFixed(2)}</span>
      </div>`;
    });

    // Take Profits
    html += `<div style="margin: 8px 0 4px 0; font-weight: bold; font-size: 12px;">Take Profits:</div>`;
    takeProfits.forEach((tp, index) => {
      const profit = Math.abs(tp.price - avgEntryPrice) * tp.qty;
      html += `<div class="trade-entry take-profit">
        ${tp.level}: ${tp.price.toFixed(2)} (Qty: ${tp.qty}) <span style="font-size: 10px;">+$${profit.toFixed(2)}</span><br>
        <span style="font-size: 10px; opacity: 0.8;">${tp.note}</span>
      </div>`;
    });

    tradeInfoEl.innerHTML = html;
  }

  // Clear trade details
  function clearTradeDetails() {
    if (tradeInfoEl) {
      tradeInfoEl.innerHTML = '<div class="muted">Draw a Fibonacci retracement to generate trade plan</div>';
    }
  }

  // Export to global scope
  window.TradeDetails = {
    initialize,
    generateTradeDetails,
    clearTradeDetails
  };

})();
