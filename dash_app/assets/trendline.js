// Trendline Module
// Handles trendline drawing, calculation, and management

(function() {
  'use strict';

  let chart = null;
  let series = null;
  let chartEl = null;
  let lastPoint = null;

  // Trendline drawing state
  let trendlineActive = false;
  let trendlineStartPoint = null;
  let tempTrendlineSeries = null;
  let lastTrendlineUpdate = 0;

  // Initialize trendline functionality
  function initialize(chartInstance, seriesInstance, chartElement) {
    chart = chartInstance;
    series = seriesInstance;
    chartEl = chartElement;
  }

  // Set last crosshair point
  function setLastPoint(point) {
    lastPoint = point;
  }

  // Helper function to calculate trendline price at a given time
  function calculateTrendlinePrice(startTime, startPrice, endTime, endPrice, targetTime) {
    if (startTime === endTime) return startPrice; // Avoid division by zero
    const slope = (endPrice - startPrice) / (endTime - startTime);
    return startPrice + slope * (targetTime - startTime);
  }

  // Helper function to create trendline series
  function createTrendlineSeries(startPoint, endPoint, opts = {}) {
    // Validate input points
    if (!startPoint || !endPoint ||
        startPoint.time == null || endPoint.time == null ||
        startPoint.price == null || endPoint.price == null) {
      console.error('Invalid trendline points:', { startPoint, endPoint });
      return null;
    }

    const trendlineSeries = chart.addLineSeries({
      color: opts.color || '#ff6b6b',
      lineWidth: opts.lineWidth || 2,
      lineStyle: opts.lineStyle || 0, // solid
      lastValueVisible: false,
      priceLineVisible: false,
      title: opts.title || 'Trendline'
    });

    // Ensure times are valid numbers (Unix timestamps)
    const startTime = typeof startPoint.time === 'number' ? startPoint.time : Math.floor(startPoint.time);
    const endTime = typeof endPoint.time === 'number' ? endPoint.time : Math.floor(endPoint.time);

    // Create trendline data points including future projection
    const trendlineData = [
      { time: startTime, value: startPoint.price },
      { time: endTime, value: endPoint.price }
    ];

    // No automatic future projection - user controls trendline endpoints

    // Set the trendline data with future projection
    try {
      trendlineSeries.setData(trendlineData);
    } catch (error) {
      console.error('Error setting trendline data:', error, { startTime, endTime, startPrice: startPoint.price, endPrice: endPoint.price });
      chart.removeSeries(trendlineSeries);
      return null;
    }

    return trendlineSeries;
  }

  // Start trendline drawing
  function startTrendlineDrawing(point, setPanZoomEnabled) {
    if (!point || typeof point.y !== 'number') return false;

    // Disable pan/zoom while dragging Trendline
    setPanZoomEnabled(false);

    const time = chart.timeScale().coordinateToTime(point.x);
    const price = LineManagement.snap(series.coordinateToPrice(point.y));

    // Validate coordinates
    if (time == null || isNaN(price)) {
      console.error('Invalid start coordinates for trendline:', { time, price });
      setPanZoomEnabled(true);
      return false;
    }

    trendlineActive = true;
    trendlineStartPoint = { x: point.x, y: point.y, time, price };

    console.log('Trendline start point:', trendlineStartPoint);

    return true;
  }

  // Update trendline drawing during mouse move
  function updateTrendlineDrawing() {
    if (!trendlineActive || !trendlineStartPoint || !lastPoint || typeof lastPoint.y !== 'number') return;

    const now = Date.now();

    // Throttle updates to prevent excessive calls (max 10 updates per second)
    if (now - lastTrendlineUpdate < 100) {
      return;
    }
    lastTrendlineUpdate = now;

    let currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
    const currentPrice = LineManagement.snap(series.coordinateToPrice(lastPoint.y));

    // Handle null currentTime in whitespace area
    if (currentTime == null) {
      const visibleRange = chart.timeScale().getVisibleRange();
      if (visibleRange) {
        const chartWidth = chartEl.clientWidth;
        const totalTimeRange = visibleRange.to - visibleRange.from;
        const mouseRatio = lastPoint.x / chartWidth;
        currentTime = visibleRange.from + (totalTimeRange * mouseRatio);
      }
    }

    console.log('Temp trendline coordinates:', { currentTime, currentPrice, startTime: trendlineStartPoint.time, startPrice: trendlineStartPoint.price });

    // Validate current coordinates and ensure different from start point
    if (currentTime != null && !isNaN(currentPrice) &&
        currentTime !== trendlineStartPoint.time &&
        Math.abs(currentPrice - trendlineStartPoint.price) > 0.001) {

      // Remove existing temp trendline
      if (tempTrendlineSeries) {
        try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
        tempTrendlineSeries = null;
      }

      // Create new temp trendline only if points are sufficiently different
      tempTrendlineSeries = createTrendlineSeries(
        { time: trendlineStartPoint.time, price: trendlineStartPoint.price },
        { time: currentTime, price: currentPrice },
        { color: '#cccccc', lineWidth: 1, lineStyle: 2, title: 'Trendline?' }
      );
    }
  }

  // Complete trendline drawing
  function completeTrendlineDrawing(setPanZoomEnabled) {
    if (!trendlineActive) return false;

    trendlineActive = false;

    // Re-enable pan/zoom
    setPanZoomEnabled(true);

    if (!lastPoint || typeof lastPoint.y !== 'number' || !trendlineStartPoint) {
      // cleanup temp trendline
      if (tempTrendlineSeries) {
        try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
        tempTrendlineSeries = null;
      }
      trendlineStartPoint = null;
      return false;
    }

    let currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
    const currentPrice = LineManagement.snap(series.coordinateToPrice(lastPoint.y));

    // Handle null currentTime in whitespace area
    if (currentTime == null) {
      const visibleRange = chart.timeScale().getVisibleRange();
      if (visibleRange) {
        const chartWidth = chartEl.clientWidth;
        const totalTimeRange = visibleRange.to - visibleRange.from;
        const mouseRatio = lastPoint.x / chartWidth;
        currentTime = visibleRange.from + (totalTimeRange * mouseRatio);

        console.log('Calculated future time for trendline completion:', {
          currentTime,
          futureDate: new Date(currentTime * 1000).toISOString(),
          mouseX: lastPoint.x,
          chartWidth
        });
      }
    }

    // Validate coordinates before proceeding
    if (currentTime == null || isNaN(currentPrice)) {
      console.error('Invalid coordinates for trendline completion:', { currentTime, currentPrice });
      if (tempTrendlineSeries) {
        try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
        tempTrendlineSeries = null;
      }
      trendlineStartPoint = null;
      return false;
    }

    // Check if start and end points are too similar (prevent invalid trendlines)
    if (currentTime === trendlineStartPoint.time ||
        Math.abs(currentPrice - trendlineStartPoint.price) < 0.001) {
      console.log('Trendline points too similar, canceling:', {
        startTime: trendlineStartPoint.time,
        endTime: currentTime,
        startPrice: trendlineStartPoint.price,
        endPrice: currentPrice
      });
      if (tempTrendlineSeries) {
        try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
        tempTrendlineSeries = null;
      }
      trendlineStartPoint = null;
      return false;
    }

    const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

    // Remove temp trendline
    if (tempTrendlineSeries) {
      try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
      tempTrendlineSeries = null;
    }

    // Create permanent trendline
    const permanentTrendlineSeries = createTrendlineSeries(
      { time: trendlineStartPoint.time, price: trendlineStartPoint.price },
      { time: currentTime, price: currentPrice },
      { color: '#ff6b6b', lineWidth: 2, title: 'Trendline' }
    );

    // Check if trendline creation was successful
    if (!permanentTrendlineSeries) {
      console.error('Failed to create permanent trendline series');
      trendlineStartPoint = null;
      return false;
    }

    // Create a single list entry for the trendline
    const trendlineId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
    const trendlineRec = {
      id: trendlineId,
      p1: trendlineStartPoint.price,
      p2: currentPrice,
      t1: trendlineStartPoint.time,
      t2: currentTime,
      kind: 'trendline',
      group,
      handle: null,
      trendlineSeries: permanentTrendlineSeries
    };

    const lines = LineManagement.getAllLines();
    lines.push(trendlineRec);

    // Manually add list item
    const listEl = document.getElementById('list');
    if (listEl) {
      const li = document.createElement('li');
      li.id = 'li-' + trendlineId;

      const left = document.createElement('div');
      left.textContent = 'Trendline ';
      const pill = document.createElement('span');
      pill.className='pill';
      pill.textContent = `${LineManagement.fmt(trendlineStartPoint.price)} → ${LineManagement.fmt(currentPrice)}`;
      left.appendChild(pill);

      const tag = document.createElement('span');
      tag.className = 'groupTag';
      tag.textContent = group.slice(0,6);
      left.appendChild(tag);

      const right = document.createElement('div');
      const del = document.createElement('button'); 
      del.className='btn'; 
      del.textContent='🗑️';
      del.onclick = () => LineManagement.removeGroup(group);
      right.appendChild(del);

      li.append(left, right);
      listEl.appendChild(li);
    }

    console.log('Trendline created:', {
      p1: trendlineStartPoint.price,
      p2: currentPrice,
      t1: trendlineStartPoint.time,
      t2: currentTime,
      // Calculate current price intersection for demonstration
      currentIntersection: calculateTrendlinePrice(
        trendlineStartPoint.time,
        trendlineStartPoint.price,
        currentTime,
        currentPrice,
        Math.floor(Date.now() / 1000) // current timestamp
      )
    });

    trendlineStartPoint = null;
    return true;
  }

  // Check if trendline is active
  function isTrendlineActive() {
    return trendlineActive;
  }

  // Export to global scope
  window.Trendline = {
    initialize,
    setLastPoint,
    startTrendlineDrawing,
    updateTrendlineDrawing,
    completeTrendlineDrawing,
    isTrendlineActive,
    calculateTrendlinePrice
  };

})();
