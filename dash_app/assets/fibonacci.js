// Fibonacci Module
// Handles all Fibonacci-related functionality including manual and auto-fib

(function() {
  'use strict';

  let chart = null;
  let series = null;
  let chartEl = null;
  let lastPoint = null;

  // Fib drawing state
  let fibActive = false;
  let fibStartPrice = null;
  let fibStartTime = null;
  let tempStart = null;
  let tempEnd = null;

  // Initialize fibonacci functionality
  function initialize(chartInstance, seriesInstance, chartElement) {
    chart = chartInstance;
    series = seriesInstance;
    chartEl = chartElement;
  }

  // Set last crosshair point
  function setLastPoint(point) {
    lastPoint = point;
  }

  // Start fibonacci drawing
  function startFibDrawing(point, setPanZoomEnabled) {
    if (!point || typeof point.y !== 'number') return false;

    // Disable pan/zoom while dragging Fib
    setPanZoomEnabled(false);

    fibActive = true;
    fibStartPrice = LineManagement.snap(series.coordinateToPrice(point.y));
    fibStartTime = chart.timeScale().coordinateToTime(point.x);

    // temp start line
    tempStart = series.createPriceLine({
      price: fibStartPrice, 
      color:'#cccccc', 
      lineWidth:1, 
      lineStyle:2, 
      axisLabelVisible:true, 
      title:'P1'
    });

    return true;
  }

  // Update fibonacci drawing during mouse move
  function updateFibDrawing() {
    if (!fibActive || !lastPoint || typeof lastPoint.y !== 'number') return;

    const y = lastPoint.y;
    const price = LineManagement.snap(series.coordinateToPrice(y));
    
    // re-create dynamic end line
    if (tempEnd) { 
      try { series.removePriceLine(tempEnd); } catch(e){} 
      tempEnd = null; 
    }
    tempEnd = series.createPriceLine({
      price,
      color:'#cccccc', 
      lineWidth:1, 
      lineStyle:2, 
      axisLabelVisible:true, 
      title:'P2?'
    });
  }

  // Complete fibonacci drawing
  function completeFibDrawing(setPanZoomEnabled) {
    if (!fibActive) return false;
    
    fibActive = false;

    // Re-enable pan/zoom
    setPanZoomEnabled(true);

    // finalize P2
    if (!lastPoint || typeof lastPoint.y !== 'number') {
      // cleanup temps
      if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
      if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }
      return false;
    }

    const fibEndPrice = LineManagement.snap(series.coordinateToPrice(lastPoint.y));
    const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

    // remove temps
    if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
    if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }

    // Get end time for time-bounded Fib levels
    const fibEndTime = chart.timeScale().coordinateToTime(lastPoint.x);

    // Create fibonacci levels
    createFibonacciLevels(fibStartPrice, fibEndPrice, fibStartTime, fibEndTime, group);

    fibStartPrice = null;
    fibStartTime = null;

    return true;
  }

  // Create all fibonacci levels
  function createFibonacciLevels(p1, p2, startTime, endTime, group) {
    // create time-bounded permanent lines
    const fibP1 = LineManagement.createFibLevel(p1, startTime, endTime, { 
      color:'#f0ad4e', lineWidth:2, kind:'fib1', group, title:'P1' 
    });
    const fibP2 = LineManagement.createFibLevel(p2, startTime, endTime, { 
      color:'#f0ad4e', lineWidth:2, kind:'fib2', group, title:'P2' 
    });

    // Calculate Fibonacci retracement levels (from high back toward low)
    const range = p2 - p1;
    const fib50 = LineManagement.snap(p1 + range * 0.5);
    const fib618 = LineManagement.snap(p2 - range * 0.618); // 61.8% retracement from high
    const fib786 = LineManagement.snap(p2 - range * 0.786); // 78.6% retracement from high

    // Calculate take profit levels (extensions beyond P2)
    const fibtp236 = LineManagement.snap(p2 + range * 0.236);
    const fibtp382 = LineManagement.snap(p2 + range * 0.382); // Changed from 61.8% to 38.2%

    // Create time-bounded retracement lines
    const p50 = LineManagement.createFibLevel(fib50, startTime, endTime, { 
      color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib50', group, title:'50%' 
    });
    const p618 = LineManagement.createFibLevel(fib618, startTime, endTime, { 
      color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib618', group, title:'61.8%' 
    });
    const p786 = LineManagement.createFibLevel(fib786, startTime, endTime, { 
      color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib786', group, title:'78.6%' 
    });

    // Create time-bounded take profit lines
    const ptp236 = LineManagement.createFibLevel(fibtp236, startTime, endTime, { 
      color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp236', group, title:'TP -23.6%' 
    });
    const ptp382 = LineManagement.createFibLevel(fibtp382, startTime, endTime, { 
      color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp382', group, title:'TP -38.2%' 
    });

    // Create a single list entry for the entire Fibonacci set
    const fibSetId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
    const fibSetRec = { 
      id: fibSetId, 
      p1: p1, 
      p2: p2, 
      kind: 'fibset', 
      group, 
      handle: null 
    };
    
    const lines = LineManagement.getAllLines();
    lines.push(fibSetRec);
    
    // Manually add list item since we're bypassing createFibLevel for the set
    const listEl = document.getElementById('list');
    if (listEl) {
      const li = document.createElement('li');
      li.id = 'li-' + fibSetId;

      const left = document.createElement('div');
      left.textContent = 'Fib Set ';
      const pill = document.createElement('span');
      pill.className='pill';
      pill.textContent = `P1: ${LineManagement.fmt(p1)} → P2: ${LineManagement.fmt(p2)}`;
      left.appendChild(pill);

      const tag = document.createElement('span');
      tag.className = 'groupTag';
      tag.textContent = group.slice(0,6);
      left.appendChild(tag);

      const right = document.createElement('div');
      const del = document.createElement('button'); 
      del.className='btn'; 
      del.textContent='🗑️';
      del.onclick = () => LineManagement.removeGroup(group);
      right.appendChild(del);

      li.append(left, right);
      listEl.appendChild(li);
    }

    console.log('Fib set: P1=', p1, ' P2=', p2, ' levels=', { fib50, fib618, fib786, fibtp236, fibtp382 });

    // Generate trade details
    console.log('Calling generateTradeDetails for normal fib with:', { p1, p2, fib50, fib618, fib786, fibtp236, fibtp382 });
    if (window.TradeDetails) {
      window.TradeDetails.generateTradeDetails(p1, p2, fib50, fib618, fib786, fibtp236, fibtp382);
    }

    return { fib50, fib618, fib786, fibtp236, fibtp382 };
  }

  // Auto-Fibonacci functionality
  function createAutoFib() {
    const currentData = series.data();
    if (!currentData || currentData.length < 10) {
      console.log('Not enough data for Auto-Fib analysis');
      return;
    }

    // Filter out whitespace points for analysis
    const realData = currentData.filter(candle => candle.open !== undefined);

    const swing = window.MarketAnalysis ? window.MarketAnalysis.findMostRecentSwing(realData) : null;
    if (!swing) {
      console.log('No valid swing found for Auto-Fib');
      return;
    }

    console.log('Auto-Fib detected swing:', swing);

    // Create Auto-Fib using time-bounded levels
    const fibStartPrice = swing.from.price;
    const fibEndPrice = swing.to.price;
    const fibStartTime = swing.from.time;
    const fibEndTime = swing.to.time;
    const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

    // Create fibonacci levels
    const levels = createFibonacciLevels(fibStartPrice, fibEndPrice, fibStartTime, fibEndTime, group);

    console.log(`Auto-Fib created: ${swing.type} swing from ${fibStartPrice.toFixed(2)} to ${fibEndPrice.toFixed(2)}`);

    // Generate trade details for Auto-Fib
    if (window.TradeDetails) {
      window.TradeDetails.generateTradeDetails(fibStartPrice, fibEndPrice, levels.fib50, levels.fib618, levels.fib786, levels.fibtp236, levels.fibtp382);
    }
  }

  // Check if fibonacci is active
  function isFibActive() {
    return fibActive;
  }

  // Export to global scope
  window.Fibonacci = {
    initialize,
    setLastPoint,
    startFibDrawing,
    updateFibDrawing,
    completeFibDrawing,
    createAutoFib,
    isFibActive
  };

})();
