import dash
from dash import html, dcc
from flask import session

from services import binance as binance_service

dash.register_page(__name__, name="TradeCraft - Chart")

# Layout: Embed charting.html (LightweightCharts) in an iframe
controls_layout = html.Div([
    html.Script(src="https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"),
    html.Iframe(
        src="/assets/charting.html",
        style={
            "width": "100%",
            "height": "90vh",  # 90% of viewport height
            "border": "0",
        },
    )
], style={
    "padding": "0",
    "margin": "0",
    "width": "100%",
    "height": "100vh",
})


def layout():
    if "user_id" not in session:
        return dcc.Location(pathname="/login", id="redirect")
    return controls_layout


def update_btc_candles(_, stored_y_range):
    # Fetch 1-hour interval candlesticks for BTC/USDT
    df = binance_service.get_candlestick_dataframe(symbol="BTCUSDT", limit=1000)
