# Minimal app to test the standalone charting.html
from dash import Dash, html, dcc

# Only include the IFrame page; let the iframe load all its own scripts.
app = Dash(
    __name__,
    # Prevent the charting asset scripts from auto-running in this document;
    # they will still be served under /assets and used by the iframe.
    assets_ignore=r".*(charting|chart-init|line-management|drawing-modes|fibonacci|trendline|rectangle|trade-details)\.js$",
)

app.layout = html.Div([
    html.Iframe(
        src="/assets/charting.html",  # absolute path to Dash assets
        style={"width": "100%", "height": "100vh", "border": "0"},
    ),
    html.Div(
        "Prices will also log in the browser console.",
        style={"color": "#8b949e", "padding": "6px 10px"},
    ),
])

if __name__ == "__main__":
    app.run(debug=False)