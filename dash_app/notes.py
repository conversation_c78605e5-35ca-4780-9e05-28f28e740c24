# app.py
from dash import Dash, html, dcc
external_scripts = ["https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"]

app = Dash(__name__,
           external_scripts=external_scripts)

app.layout = html.Div(
    children=[
        html.Iframe(
            src="assets/charting.html",
            style={"width": "100%", "height": "100%", "border": "0"}
        ),
        html.Div("Prices will also log in the browser console.", style={"color": "#8b949e", "padding": "6px 10px"})
    ]
)
if __name__ == "__main__":
    app.run(debug=False)